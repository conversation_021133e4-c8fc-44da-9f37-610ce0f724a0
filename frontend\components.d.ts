/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppBottomNav: typeof import('./src/components/layout/AppBottomNav.vue')['default']
    AppLayout: typeof import('./src/components/layout/AppLayout.vue')['default']
    AppNavbar: typeof import('./src/components/layout/AppNavbar.vue')['default']
    AppSidebar: typeof import('./src/components/layout/AppSidebar.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    LanguageSwitcher: typeof import('./src/components/common/LanguageSwitcher.vue')['default']
    QuickNav: typeof import('./src/components/layout/QuickNav.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
