// 全局样式文件

// 变量定义
@use './variables.scss' as *;

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 
               'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
}

// 链接样式
a {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color 0.3s;

  &:hover {
    color: var(--el-color-primary-dark-2);
  }
}

// 按钮样式增强
.el-button {
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
  }

  &.el-button--large {
    padding: 12px 20px;
    font-size: 16px;
  }
}

// 卡片样式
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

// 页面容器
.page-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

// 页面标题
.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary);
  display: inline-block;
}

// 表单样式增强
.form-container {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .el-form-item__label {
    font-weight: 500;
  }
}

// 表格样式增强
.el-table {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header {
    background: #f8f9fa;
  }

  .el-table__row:hover {
    background: #f5f7fa;
  }
}

// 分页样式
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  flex-direction: column;

  .loading-text {
    margin-top: 15px;
    color: var(--el-text-color-regular);
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--el-text-color-regular);

  .empty-icon {
    font-size: 64px;
    color: var(--el-text-color-placeholder);
    margin-bottom: 20px;
  }

  .empty-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--el-text-color-regular);
  }

  .empty-description {
    font-size: 14px;
    color: var(--el-text-color-placeholder);
  }
}

// 统计卡片
.stat-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
    color: white;
  }

  .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .stat-change {
    font-size: 12px;
    margin-top: 8px;

    &.positive {
      color: var(--el-color-success);
    }

    &.negative {
      color: var(--el-color-danger);
    }
  }
}

// 响应式网格
.grid {
  display: grid;
  gap: 20px;

  &.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  &.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  &.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }

.p-10 { padding: 10px; }
.p-20 { padding: 20px; }
.p-30 { padding: 30px; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}

.w-full { width: 100%; }
.h-full { height: 100%; }

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
  
  &:hover {
    background: var(--el-border-color-darker);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// 响应式断点
@media (max-width: 768px) {
  .content-wrapper {
    padding: 15px;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 20px;
  }

  .form-container {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 10px;
  }

  .card {
    padding: 15px;
  }

  .stat-card {
    padding: 20px;
  }
}
