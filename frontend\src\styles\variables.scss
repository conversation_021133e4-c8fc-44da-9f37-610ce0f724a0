// SCSS 变量定义

// 颜色变量
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #C0C4CC;

// 背景颜色
$bg-color: #FFFFFF;
$bg-color-page: #F2F3F5;
$bg-color-overlay: rgba(255, 255, 255, 0.9);

// 边框颜色
$border-color: #DCDFE6;
$border-color-light: #E4E7ED;
$border-color-lighter: #EBEEF5;
$border-color-extra-light: #F2F6FC;

// 填充颜色
$fill-color: #F0F2F5;
$fill-color-light: #F5F7FA;
$fill-color-lighter: #FAFAFA;
$fill-color-extra-light: #FAFCFF;
$fill-color-dark: #EBEDF0;
$fill-color-darker: #E6E8EB;
$fill-color-blank: #FFFFFF;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 字体粗细
$font-weight-primary: 500;
$font-weight-bold: 700;

// 行高
$line-height-primary: 24px;
$line-height-large: 26px;

// 边框半径
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-round: 20px;
$border-radius-circle: 100%;

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 组件尺寸
$component-size-large: 40px;
$component-size-default: 32px;
$component-size-small: 24px;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// Z-index
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;

// 过渡动画
$transition-duration: 0.3s;
$transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
$transition-all: all $transition-duration $transition-function-ease-in-out-bezier;
$transition-fade: opacity $transition-duration $transition-function-fast-bezier;
$transition-md-fade: transform $transition-duration $transition-function-fast-bezier, opacity $transition-duration $transition-function-fast-bezier;
$transition-fade-linear: opacity $transition-duration linear;
$transition-border: border-color $transition-duration $transition-function-ease-in-out-bezier;
$transition-box-shadow: box-shadow $transition-duration $transition-function-ease-in-out-bezier;
$transition-color: color $transition-duration $transition-function-ease-in-out-bezier;

// 自定义颜色
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
$gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);

// 主题色调
$theme-colors: (
  'primary': $primary-color,
  'success': $success-color,
  'warning': $warning-color,
  'danger': $danger-color,
  'info': $info-color
);

// 响应式混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (max-width: #{$breakpoint-xl - 1px}) {
      @content;
    }
  }
}

// 文本省略混合器
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 清除浮动混合器
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 居中混合器
@mixin center($position: absolute) {
  position: $position;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 按钮样式混合器
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;

  &:hover,
  &:focus {
    color: $color;
    background-color: darken($background, 7.5%);
    border-color: darken($border, 10%);
  }

  &:active {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 12.5%);
  }
}
