{"name": "driving-exam-frontend", "version": "1.0.0", "description": "驾考助手前端 - Vue.js版本", "private": true, "scripts": {"dev": "vite --port 3000", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.0", "axios": "^1.6.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "^2.4.0", "js-cookie": "^3.0.5", "vue": "^3.4.0", "vue-echarts": "^6.6.0", "vue-i18n": "^9.14.4", "vue-router": "^4.2.5", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "prettier": "^3.1.0", "sass": "^1.69.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}